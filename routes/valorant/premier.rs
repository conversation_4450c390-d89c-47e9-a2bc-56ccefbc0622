use crate::methods::utils::RateLimiting;
use crate::structs::database::{ PremierSeasonDB, PremierTeamDB };
use crate::structs::helper::{
	fetch_premier_matches_by_team_id,
	fetch_premier_team_by_id,
	fetch_premier_team_by_name,
	get_c_season_premier,
	get_gamepod_by_id,
	get_maps,
	get_premier_conferences,
};
use crate::structs::parser::{ parse_premier_team, parse_premier_team_history, parse_premier_team_lite };
use crate::structs::paths::{ PremierByIDPath, PremierByNamePath, PremierLeaderboardPath, PremierSearchQuery, PremierSeasonPath };
use crate::structs::responses::{
	PremierConferenceResponse,
	PremierConferenceResponseData,
	PremierConferenceResponseDataPods,
	PremierSearchResponse,
	PremierSeasonResponseV1DataEventMapSelection,
	PremierSeasonV1Response,
	PremierSeasonV1ResponseData,
	PremierSeasonV1ResponseDataEvent,
	PremierSeasonV1ResponseDataEventConferenceSchedule,
	PremierSeasonV1ResponseDataEventMapSelectionMaps,
	PremierSeasonV1ResponseDataScheduledEvent,
};
use crate::{ check_affinity, error_handler, get_db, AppState, ErrorCodes };
use axum::body::Body;
use axum::extract::{ Path, Query, State };
use axum::response::Response;
use axum::Extension;
use futures::TryStreamExt;
use mongodb::bson;
use mongodb::bson::{ doc, Document };
use std::sync::Arc;
use uuid::Uuid;

#[utoipa::path(
	get,
	path = "/valorant/v1/premier/{name}/{tag}",
	tag = "valorant",
	params(
		("name" = String, Path, description = "Team name"),
		("tag" = String, Path, description = "Team tag")
	),
	responses((status = 200, description = "OK"), (status = 400, description = "Client error"))
)]
pub async fn premier_by_name(Path(path): Path<PremierByNamePath>, State(app_state): State<Arc<AppState>>) -> Response {
	let c_season = get_c_season_premier().await;
	let client = app_state.client.clone();
	let premier_team = fetch_premier_team_by_name(&path.name, &path.tag, c_season.as_str(), &client).await;
	if premier_team.is_none() {
		return error_handler(vec![ErrorCodes::PremierTeamNotFound]);
	}
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(Body::from(serde_json::to_string(&parse_premier_team(premier_team.unwrap())).unwrap()))
		.unwrap()
}

#[utoipa::path(
	get,
	path = "/valorant/v1/premier/{name}/{tag}/history",
	tag = "valorant",
	params(
		("name" = String, Path, description = "Team name"),
		("tag" = String, Path, description = "Team tag")
	),
	responses((status = 200, description = "OK"), (status = 400, description = "Client error"))
)]
pub async fn premier_by_name_history(
	Path(path): Path<PremierByNamePath>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let c_season = get_c_season_premier().await.to_string();
	let client = app_state.client.clone();
	let premier_team = fetch_premier_team_by_name(&path.name, &path.tag, c_season.as_str(), &client).await;
	let rl = extension.as_ref();
	match premier_team {
		Some(mut team) => {
			//let redis = app_state.pool.clone();
			//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
			let conn = app_state.redis.clone();
			let games = fetch_premier_matches_by_team_id(team.clone(), conn).await;
			rl.redis_cache_ttl.store(games.redis_cache_ttl, std::sync::atomic::Ordering::SeqCst);
			rl.background_requests.fetch_add(games.background_requests, std::sync::atomic::Ordering::SeqCst);
			let members_doc: Vec<Document> = games.members
				.iter()
				.map(|member| bson::to_document(member).unwrap())
				.collect();
			let games_league_doc: Vec<Document> = games.league
				.iter()
				.map(|game| bson::to_document(game).unwrap())
				.collect();
			let games_tournament_doc: Vec<Document> = games.tournament
				.iter()
				.map(|game| bson::to_document(game).unwrap())
				.collect();
			team.games.league = games.league;
			team.games.tournament = games.tournament;
			let _ = get_db::<PremierTeamDB>(&client, "premier_teams", None).update_one(
				doc! { "id": team.id.clone(), "season": c_season },
				doc! { "$set": doc! {"member": members_doc, "games.league": games_league_doc, "games.tournament": games_tournament_doc} }
			).await;
			Response::builder()
				.status(200)
				.header("Content-Type", "application/json")
				.body(Body::from(serde_json::to_string(&parse_premier_team_history(team)).unwrap()))
				.unwrap()
		}
		None => error_handler(vec![ErrorCodes::PremierTeamNotFound]),
	}
}

#[utoipa::path(
	get,
	path = "/valorant/v1/premier/{id}",
	tag = "valorant",
	params(
		("id" = String, Path, description = "Team UUID")
	),
	responses((status = 200, description = "OK"), (status = 400, description = "Client error"))
)]
pub async fn premier_by_id(Path(path): Path<PremierByIDPath>, State(app_state): State<Arc<AppState>>, Extension(_extension): Extension<Arc<RateLimiting>>) -> Response {
	let validate_uuid = Uuid::parse_str(&path.id);
	if validate_uuid.is_err() {
		return error_handler(vec![ErrorCodes::InvalidUUID]);
	}
	let c_season = get_c_season_premier().await;
	let client = app_state.client.clone();
	let premier_team = fetch_premier_team_by_id(&path.id, c_season.as_str(), &client).await;
	if premier_team.is_none() {
		return error_handler(vec![ErrorCodes::PremierTeamNotFound]);
	}
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(Body::from(serde_json::to_string(&parse_premier_team(premier_team.unwrap())).unwrap()))
		.unwrap()
}

#[utoipa::path(
	get,
	path = "/valorant/v1/premier/{id}/history",
	tag = "valorant",
	params(
		("id" = String, Path, description = "Team UUID")
	),
	responses((status = 200, description = "OK"), (status = 400, description = "Client error"))
)]
pub async fn premier_by_id_history(
	Path(path): Path<PremierByIDPath>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let validate_uuid = Uuid::parse_str(&path.id);
	if validate_uuid.is_err() {
		return error_handler(vec![ErrorCodes::InvalidUUID]);
	}
	let c_season = get_c_season_premier().await.to_string();
	let client = app_state.client.clone();
	let rl = extension.as_ref();
	let premier_team = fetch_premier_team_by_id(&path.id, c_season.as_str(), &client).await;
	match premier_team {
		Some(mut team) => {
			//let redis = app_state.pool.clone();
			//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
			let conn = app_state.redis.clone();
			let games = fetch_premier_matches_by_team_id(team.clone(), conn).await;
			rl.redis_cache_ttl.store(games.redis_cache_ttl, std::sync::atomic::Ordering::SeqCst);
			rl.background_requests.fetch_add(games.background_requests, std::sync::atomic::Ordering::SeqCst);
			let members_doc: Vec<Document> = games.members
				.iter()
				.map(|member| bson::to_document(member).unwrap())
				.collect();
			let games_league_doc: Vec<Document> = games.league
				.iter()
				.map(|game| bson::to_document(game).unwrap())
				.collect();
			let games_tournament_doc: Vec<Document> = games.tournament
				.iter()
				.map(|game| bson::to_document(game).unwrap())
				.collect();
			team.games.league = games.league;
			team.games.tournament = games.tournament;
			let _ = get_db::<PremierTeamDB>(&client, "premier_teams", None).update_one(
				doc! { "id": team.id.clone(), "season": c_season },
				doc! { "$set": doc! {"member": members_doc, "games.league": games_league_doc, "games.tournament": games_tournament_doc} }
			).await;
			Response::builder()
				.status(200)
				.header("Content-Type", "application/json")
				.body(Body::from(serde_json::to_string(&parse_premier_team_history(team)).unwrap()))
				.unwrap()
		}
		None => error_handler(vec![ErrorCodes::PremierTeamNotFound]),
	}
}

#[utoipa::path(
	get,
	path = "/valorant/v1/premier/search",
	tag = "valorant",
	params(
		("name" = Option<String>, Query, description = "Team name to search for (optional)"),
		("tag" = Option<String>, Query, description = "Team tag to search for (optional)"),
		("id" = Option<String>, Query, description = "Team UUID to search for (optional)")
	),
	responses((status = 200, description = "OK"), (status = 400, description = "Client error"))
)]
pub async fn premier_search(Query(query): Query<PremierSearchQuery>, State(app_state): State<Arc<AppState>>) -> Response {
	let mut search_query = doc! {};
	let mut errors: Vec<ErrorCodes> = vec![];
	if query.name.is_some() {
		search_query.insert("name", query.name.clone().unwrap().replace('$', String::new().as_str()));
	}
	if query.tag.is_some() {
		search_query.insert("tag", query.tag.clone().unwrap().replace('$', String::new().as_str()));
	}
	if query.division.is_some() {
		let div_query = query.division.clone().unwrap();
		let div = div_query.parse::<i32>();
		if div.is_err() {
			errors.push(ErrorCodes::PremierSearchInvalidDivision);
		} else {
			let division = div.unwrap();
			if division < 1 || division > 21 {
				errors.push(ErrorCodes::PremierSearchInvalidDivisionSize);
			}
			search_query.insert("division", division);
		}
	}
	if query.conference.is_some() {
		let conferences = get_premier_conferences().await;
		let conference = query.conference.clone().unwrap().to_uppercase();
		if !conferences.iter().any(|i| i.conference.key == conference) {
			errors.push(ErrorCodes::PremierSearchInvalidConference);
		}
		search_query.insert("conference", conference);
	}
	if query.id.is_some() {
		let validate_uuid = Uuid::parse_str(&query.id.clone().unwrap());
		if validate_uuid.is_err() {
			return error_handler(vec![ErrorCodes::InvalidUUID]);
		}
		if query.name.is_some() || query.tag.is_some() {
			errors.push(ErrorCodes::PremierSearchMultipleQueries);
		}
		search_query.insert("id", query.id.clone().unwrap());
	}
	if errors.len() > 0 {
		return error_handler(errors);
	}
	let client = app_state.client.clone();
	let teams = get_db::<PremierTeamDB>(&client, "premier_teams", None)
		.find(search_query.clone())
		.limit(if search_query.len() > 0 { 0 } else { 50 })
		.sort(doc! { "division": 1, "stats.ranking": 1 }).await;
	if teams.is_err() {
		return error_handler(vec![ErrorCodes::InternalError]);
	}
	let collected: Vec<PremierTeamDB> = teams
		.unwrap()
		.try_collect().await
		.unwrap_or_else(|e| {
			eprintln!("[ENDPOINT][/valorant/v1/premier/search] Error collecting from DB: {:?}", e);
			vec![]
		});
	let response = PremierSearchResponse {
		status: 200,
		data: parse_premier_team_lite(collected),
	};
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(Body::from(serde_json::to_string(&response).unwrap()))
		.unwrap()
}

pub async fn premier_conferences() -> Response {
	let conferences = get_premier_conferences().await;
	let mut data: Vec<PremierConferenceResponseData> = vec![];
	for i in conferences.iter() {
		let mut pods: Vec<PremierConferenceResponseDataPods> = vec![];
		for k in i.conference.gamePods.iter() {
			pods.push(PremierConferenceResponseDataPods {
				pod: k.clone(),
				name: match get_gamepod_by_id(k).await {
					Some(pod) => Some(pod.clone()),
					None => None,
				},
			});
		}
		let all_data = PremierConferenceResponseData {
			id: i.id.clone(),
			affinity: i.affinity.clone(),
			pods,
			region: i.region.clone(),
			name: i.conference.key.clone(),
			timezone: i.conference.timezone.clone(),
			icon: format!("https://cdn.henrikdev.xyz/valorant/v1/premier/conference/{}", i.id.clone()),
		};
		data.push(all_data);
	}
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(Body::from(serde_json::to_string(&(PremierConferenceResponse { status: 200, data })).unwrap()))
		.unwrap()
}

pub async fn premier_seasons(Path(path): Path<PremierSeasonPath>, State(app_state): State<Arc<AppState>>) -> Response {
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let client = app_state.client.clone();
	let seasons = get_db::<PremierSeasonDB>(&client, "premier_seasons", None).find(doc! { "affinity": path.affinity.clone() }).await;
	if seasons.is_err() {
		return error_handler(vec![ErrorCodes::InternalError]);
	}
	let mut collected: Vec<PremierSeasonDB> = seasons
		.unwrap()
		.try_collect().await
		.unwrap_or_else(|e| {
			eprintln!("[ENDPOINT][/valorant/v1/premier/seasons] Error collecting from DB: {:?}", e);
			vec![]
		});
	collected.sort_by(|a, b| a.season.StartTime.cmp(&b.season.StartTime));
	let maps = get_maps().await;
	let response = PremierSeasonV1Response {
		status: 200,
		data: collected
			.iter()
			.map(|season| PremierSeasonV1ResponseData {
				id: season.id.clone(),
				championship_event_id: season.season.ChampionshipEventID.clone(),
				championship_points_required: season.season.ChampionshipPointRequirement.clone(),
				starts_at: season.season.StartTime.clone(),
				ends_at: season.season.EndTime.clone(),
				enrollment_starts_at: season.season.EnrollmentPhaseStartDateTime.clone(),
				enrollment_ends_at: season.season.EnrollmentPhaseEndDateTime.clone(),
				events: season.season.Events
					.iter()
					.map(|event| PremierSeasonV1ResponseDataEvent {
						id: event.ID.clone(),
						type_: event.Type.clone(),
						starts_at: event.StartDateTime.clone(),
						ends_at: event.EndDateTime.clone(),
						conference_schedules: if event.SchedulePerConference.is_some() {
							event.SchedulePerConference
								.clone()
								.unwrap()
								.values()
								.map(|schedule| {
									PremierSeasonV1ResponseDataEventConferenceSchedule {
										conference: schedule.Conference.clone(),
										starts_at: schedule.StartDateTime.clone(),
										ends_at: schedule.EndDateTime.clone(),
									}
								})
								.collect::<Vec<PremierSeasonV1ResponseDataEventConferenceSchedule>>()
						} else {
							vec![]
						},
						map_selection: PremierSeasonResponseV1DataEventMapSelection {
							type_: event.MapSelectionStrategy.clone(),
							maps: event.MapPoolMapIDs
								.iter()
								.map(|_map| {
									let map = maps.iter().find(|i| &i.uuid == _map);
									PremierSeasonV1ResponseDataEventMapSelectionMaps {
										name: match map {
											Some(m) => m.displayName.clone(),
											None => String::new(),
										},
										id: _map.clone(),
									}
								})
								.collect::<Vec<PremierSeasonV1ResponseDataEventMapSelectionMaps>>(),
						},
						points_required_to_participate: event.PointsRequiredToParticipate.clone(),
					})
					.collect::<Vec<PremierSeasonV1ResponseDataEvent>>(),
				scheduled_events: season.season.ScheduledEvents
					.iter()
					.map(|event| PremierSeasonV1ResponseDataScheduledEvent {
						event_id: event.EventID.clone(),
						conference: event.Conference.clone(),
						starts_at: event.StartDateTime.clone(),
						ends_at: event.EndDateTime.clone(),
					})
					.collect::<Vec<PremierSeasonV1ResponseDataScheduledEvent>>(),
			})
			.collect::<Vec<PremierSeasonV1ResponseData>>(),
	};
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(Body::from(serde_json::to_string(&response).unwrap()))
		.unwrap()
}

pub async fn premier_leaderboard(Path(path): Path<PremierLeaderboardPath>, State(app_state): State<Arc<AppState>>) -> Response {
	let mut search_query = doc! {};
	let mut errors: Vec<ErrorCodes> = vec![];
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		errors.push(ErrorCodes::InvalidRegion);
	} else {
		let season = get_c_season_premier().await.to_string();
		search_query.insert("affinity", path.affinity.clone());
		search_query.insert("season", season);
	}
	if path.conference.is_some() {
		let conferences = get_premier_conferences().await;
		let conference = path.conference.clone().unwrap().to_uppercase();
		if !conferences.iter().any(|i| i.conference.key == conference) {
			errors.push(ErrorCodes::PremierSearchInvalidConference);
		}
		search_query.insert("conference", conference);
	}
	if path.division.is_some() {
		let div_query = path.division.clone().unwrap();
		let div = div_query.parse::<i32>();
		if div.is_err() {
			errors.push(ErrorCodes::PremierSearchInvalidDivision);
		} else {
			let division = div.unwrap();
			println!("{:?}", division);
			println!("{:?}", path.conference.is_some());
			if path.conference.is_some() && path.conference.clone().unwrap().to_uppercase().contains("_SUPER") && division != 22 {
				errors.push(ErrorCodes::PremierSearchInvalidDivisionSizeSuper);
			} else if path.conference.is_some() && !path.conference.clone().unwrap().to_uppercase().contains("_SUPER") && (division < 1 || division > 21) {
				errors.push(ErrorCodes::PremierSearchInvalidDivisionSize);
			}
			search_query.insert("division", division);
		}
	}
	if errors.len() > 0 {
		return error_handler(errors);
	}
	let client = app_state.client.clone();
	let teams = get_db::<PremierTeamDB>(&client, "premier_teams", None)
		.find(search_query.clone())
		.sort(doc! { "division": -1, "stats.ranking": 1 }).await;
	if teams.is_err() {
		eprintln!("[ENDPOINT][/valorant/v1/premier/leaderboard] Error fetching from DB: {:?}", teams.err().unwrap());
		return error_handler(vec![ErrorCodes::InternalError]);
	}
	let collected: Vec<PremierTeamDB> = teams
		.unwrap()
		.try_collect().await
		.unwrap_or_else(|e| {
			eprintln!("[ENDPOINT][/valorant/v1/premier/leaderboard] Error collecting from DB: {:?}", e);
			vec![]
		});
	let response = PremierSearchResponse {
		status: 200,
		data: parse_premier_team_lite(collected),
	};
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(Body::from(serde_json::to_string(&response).unwrap()))
		.unwrap()
}
