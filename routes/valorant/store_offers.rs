use crate::methods::utils::RateLimiting;
use crate::structs::enums::RiotPlatforms;
use crate::structs::helper::{
    get_buddies, get_content_tiers, get_player_cards, get_player_titles, get_skins, get_sprays,
};
use crate::structs::http_clients::{redis_fetch_ipv6, RedisFetchIPv6Options};
use crate::structs::paths::StoreFeaturedPath;
use crate::structs::responses::{
    StoreOffersV1, StoreOffersV1Offer, StoreOffersV1Response, StoreOffersV2Offer,
    StoreOffersV2OfferContentTier, StoreOffersV2Response,
};
use crate::{build_riot_headers, error_handler, AppState, ErrorCodes, VALORANT_TYPE_IDS};
use axum::body::Body;
use axum::extract::{Path, State};
use axum::response::Response;
use axum::Extension;
use std::sync::Arc;

#[utoipa::path(
	get,
	path = "/valorant/{version}/store-offers",
	tag = "valorant",
	params(
		("version" = String, Path, description = "API version (v1, v2)")
	),
	responses((status = 200, description = "OK"), (status = 400, description = "Client error"))
)]
#[allow(non_snake_case)]
pub async fn StoreOffers(
    Path(path): Path<StoreFeaturedPath>,
    State(app_state): State<Arc<AppState>>,
    Extension(extension): Extension<Arc<RateLimiting>>,
) -> Response {
    //return
    return error_handler(vec![ErrorCodes::RiotImplementationRemoved]);

    let rl = extension.as_ref();
    let headers = build_riot_headers(&RiotPlatforms::PC).await;
    //let conn = redis.get().await.expect("Failed to get Redis connection from pool");
    let conn = app_state.redis.clone();
    let store = redis_fetch_ipv6::<StoreOffersV1>(RedisFetchIPv6Options {
        url: "https://pd.eu.a.pvp.net/store/v1/offers/".to_string(),
        headers,
        redis_client: Some(conn),
        store: "store_offers".to_string(),
        ..RedisFetchIPv6Options::default()
    })
    .await;
    if store.is_err() {
        return error_handler(vec![ErrorCodes::FetchingResource]);
    }
    let store_json = store.unwrap();
    rl.redis_cache_ttl
        .store(store_json.ttl as isize, std::sync::atomic::Ordering::SeqCst);
    if !store_json.is_from_redis {
        rl.background_requests
            .fetch_add(1, std::sync::atomic::Ordering::SeqCst);
    }
    return match path.version.as_str() {
        "v1" => Response::builder()
            .status(200)
            .header("Content-Type", "application/json")
            .body(Body::from(
                serde_json::to_string(&StoreOffersV1Response {
                    status: 200,
                    data: store_json.data,
                })
                .unwrap(),
            ))
            .unwrap(),
        "v2" => {
            let parsed_offers: Vec<StoreOffersV2Offer> =
                v2_item_parser(store_json.data.Offers).await;
            Response::builder()
                .status(200)
                .header("Content-Type", "application/json")
                .body(Body::from(
                    serde_json::to_string(&StoreOffersV2Response {
                        status: 200,
                        data: parsed_offers,
                    })
                    .unwrap(),
                ))
                .unwrap()
        }
        _ => error_handler(vec![ErrorCodes::UnknownVersion]),
    };
}

async fn v2_item_parser(items: Vec<StoreOffersV1Offer>) -> Vec<StoreOffersV2Offer> {
    let mut formatted: Vec<StoreOffersV2Offer> = vec![];
    let skins = get_skins().await;
    let buddies = get_buddies().await;
    let player_cards = get_player_cards().await;
    let player_titles = get_player_titles().await;
    let sprays = get_sprays().await;
    let content_tiers = get_content_tiers().await;
    let item_type = &VALORANT_TYPE_IDS;
    for x in items
        .iter()
        .filter(|x| x.Rewards.get(0).unwrap().ItemTypeID != "ea6fcd2e-8373-4137-b1c0-b458947aa86d")
    {
        let item_type = item_type
            .get(x.Rewards.get(0).unwrap().ItemTypeID.as_str())
            .unwrap();
        let parent: PartialParentData = match *item_type {
            "skin_level" => {
                let parent = skins
                    .iter()
                    .find(|&i| {
                        i.levels
                            .iter()
                            .find(|&k| k.uuid == x.Rewards.get(0).unwrap().ItemID)
                            .is_some()
                    })
                    .unwrap();
                let clone = parent.contentTierUuid.clone();
                let uuid = clone.unwrap();
                let content_tier = content_tiers.iter().find(|&i| i.uuid == uuid).unwrap();
                PartialParentData {
                    name: Some(parent.displayName.to_string()),
                    icon: parent
                        .levels
                        .iter()
                        .find(|&y| y.uuid == x.Rewards.get(0).unwrap().ItemID)
                        .unwrap()
                        .displayIcon
                        .clone(),
                    skin_id: Some(parent.uuid.to_string()),
                    content_tier: Some(StoreOffersV2OfferContentTier {
                        name: content_tier.displayName.to_string(),
                        dev_name: content_tier.devName.to_string(),
                        icon: content_tier.displayIcon.to_string(),
                    }),
                }
            }
            "buddy" => {
                let parent = buddies
                    .iter()
                    .find(|&i| {
                        i.levels
                            .iter()
                            .find(|&k| k.uuid == x.Rewards.get(0).unwrap().ItemID)
                            .is_some()
                    })
                    .unwrap();
                PartialParentData {
                    name: Some(parent.displayName.to_string()),
                    icon: Some(parent.displayIcon.clone()),
                    skin_id: Some(parent.uuid.to_string()),
                    content_tier: None,
                }
            }
            "player_card" => {
                let parent = player_cards
                    .iter()
                    .find(|&i| i.uuid == x.Rewards.get(0).unwrap().ItemID)
                    .unwrap();
                PartialParentData {
                    name: Some(parent.displayName.to_string()),
                    icon: Some(parent.displayIcon.clone()),
                    skin_id: Some(parent.uuid.to_string()),
                    content_tier: None,
                }
            }
            "player_title" => {
                let parent = player_titles
                    .iter()
                    .find(|&i| i.uuid == x.Rewards.get(0).unwrap().ItemID)
                    .unwrap();
                PartialParentData {
                    name: Some(
                        parent
                            .titleText
                            .clone()
                            .unwrap_or(parent.displayName.clone().unwrap()),
                    ),
                    icon: None,
                    skin_id: Some(parent.uuid.to_string()),
                    content_tier: None,
                }
            }
            "spray" => {
                let parent = sprays
                    .iter()
                    .find(|&i| i.uuid == x.Rewards.get(0).unwrap().ItemID)
                    .unwrap();
                PartialParentData {
                    name: Some(parent.displayName.to_string()),
                    icon: Some(parent.displayIcon.clone()),
                    skin_id: Some(parent.uuid.to_string()),
                    content_tier: None,
                }
            }
            _ => PartialParentData {
                name: None,
                icon: None,
                skin_id: None,
                content_tier: None,
            },
        };
        formatted.push(StoreOffersV2Offer {
            offer_id: x.OfferID.clone(),
            cost: x
                .Cost
                .get("85ad13f7-3d1b-5128-9eb2-7cd8ee0b5741")
                .unwrap()
                .unsigned_abs(),
            name: parent.name,
            icon: parent.icon,
            r#type: item_type.to_string(),
            skin_id: parent.skin_id,
            content_tier: parent.content_tier,
        })
    }
    formatted
}

pub struct PartialParentData {
    pub name: Option<String>,
    pub icon: Option<String>,
    pub skin_id: Option<String>,
    pub content_tier: Option<StoreOffersV2OfferContentTier>,
}
